# 微信小程序AI助手 - 音乐分包

这是微信小程序AI助手的音乐分包，包含了音乐搜索和播放功能。

## 功能特点

- 音乐搜索：支持搜索本地音乐库
- 音乐播放：支持在线播放音乐，包括后台播放
- 音乐历史：记录搜索历史，方便快速访问

## 使用说明

1. 启动音乐服务：
   - 运行 `start_music.bat` 脚本启动本地音乐服务
   - 确保音乐服务在端口5000上运行

2. 配置API地址：
   - 在 `config.js` 中设置正确的音乐API地址
   - 默认地址为 `http://127.0.0.1:5000`
   - 在手机上测试时，需要修改为电脑的IP地址

3. 添加音乐：
   - 首次使用需要添加本地音乐文件
   - 将MP3文件放入主项目的 `download/music` 目录
   - 运行主项目中的 `add_music.bat` 脚本索引音乐文件

## 注意事项

- 音乐文件大小超过2MB的会自动转码为较小的文件
- 请确保Python环境已正确安装
- 如遇到问题，请检查网络连接和API配置

## 目录结构

- `pages/music`: 音乐页面相关文件
- `images`: 图标和界面资源
- `utils`: 工具函数
- `config.js`: 全局配置文件
- `start_music.bat`: 音乐服务启动脚本 