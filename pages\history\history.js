// history.js
Page({
  data: {
    historyList: [],
    loading: true,
    activeTab: 'all', // 'all', 'chat', 'translate', 'image', 'voice'
    tabs: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'chat', name: '对话', count: 0 },
      { id: 'translate', name: '翻译', count: 0 },
      { id: 'image', name: '图像', count: 0 },
      { id: 'voice', name: '语音', count: 0 }
    ],
    isEmpty: false,
    isGuestMode: false, // 是否是游客模式
    animation: {},
    lastRefreshTime: '刚刚更新',
    currentTabName: '全部' // 添加当前标签名称
  },

  onLoad() {
    // 检查是否是游客模式
    const isGuestMode = wx.getStorageSync('isGuestMode') || false;
    this.setData({ isGuestMode });
    
    // 加载历史数据
    this.loadHistoryData();
    
    // 创建动画实例
    this.animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease',
    });
  },

  onShow() {
    // 每次显示页面时重新检查游客模式
    const isGuestMode = wx.getStorageSync('isGuestMode') || false;
    if (isGuestMode !== this.data.isGuestMode) {
      this.setData({ isGuestMode });
    }
    
    // 每次显示页面时重新加载数据
    this.loadHistoryData();
    
    // 更新最后刷新时间
    this.updateRefreshTime();
  },

  onPullDownRefresh() {
    this.loadHistoryData();
    this.updateRefreshTime();
    wx.stopPullDownRefresh();
    
    // 显示刷新成功提示
    wx.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000
    });
  },
  
  // 更新最后刷新时间
  updateRefreshTime() {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const formattedTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
    this.setData({
      lastRefreshTime: `${formattedTime} 更新`
    });
  },
  
  // 获取当前标签名称
  getTabName() {
    const { tabs, activeTab } = this.data;
    for (let i = 0; i < tabs.length; i++) {
      if (tabs[i].id === activeTab) {
        return tabs[i].name;
      }
    }
    return '全部';
  },

  loadHistoryData(tab = this.data.activeTab) {
    console.log('加载历史数据，标签:', tab);
    this.setData({ loading: true, isEmpty: false });
    
    // 创建测试数据（如果本地存储中没有数据）
    this.createTestDataIfNeeded();
    
    // 获取所有历史记录
    const chatHistory = wx.getStorageSync('chatHistory') || [];
    const translateHistory = wx.getStorageSync('translateHistory') || [];
    const imageHistory = wx.getStorageSync('imageHistory') || [];
    const voiceHistory = wx.getStorageSync('voiceHistory') || [];
    
    // 更新标签页数量
    const tabs = this.data.tabs.map(item => {
      if (item.id === 'all') {
        item.count = chatHistory.length + translateHistory.length + imageHistory.length + voiceHistory.length;
      } else if (item.id === 'chat') {
        item.count = chatHistory.length;
      } else if (item.id === 'translate') {
        item.count = translateHistory.length;
      } else if (item.id === 'image') {
        item.count = imageHistory.length;
      } else if (item.id === 'voice') {
        item.count = voiceHistory.length;
      }
      return item;
    });
    
    // 获取当前标签名称
    let currentTabName = '全部';
    for (let i = 0; i < tabs.length; i++) {
      if (tabs[i].id === tab) {
        currentTabName = tabs[i].name;
        break;
      }
    }
    
    // 合并所有历史记录，并添加类型标识
    let allHistory = [];
    
    if (tab === 'all' || tab === 'chat') {
      allHistory = [...allHistory, ...chatHistory.map(item => ({ 
        ...item, 
        type: 'chat', 
        typeText: '对话',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'translate') {
      allHistory = [...allHistory, ...translateHistory.map(item => ({ 
        ...item, 
        type: 'translate', 
        typeText: '翻译',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'image') {
      allHistory = [...allHistory, ...imageHistory.map(item => ({ 
        ...item, 
        type: 'image', 
        typeText: '图像',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'voice') {
      allHistory = [...allHistory, ...voiceHistory.map(item => ({ 
        ...item, 
        type: 'voice', 
        typeText: '语音',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    // 按时间排序（从新到旧）
    allHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    
    console.log('加载了', allHistory.length, '条记录，当前标签:', tab);
    
    // 使用动画效果更新列表
    setTimeout(() => {
      this.setData({
        historyList: allHistory,
        tabs: tabs,
        loading: false,
        isEmpty: allHistory.length === 0,
        currentTabName: currentTabName
      });
    }, 300);
  },
  
  // 创建测试数据（仅用于演示）
  createTestDataIfNeeded() {
    const hasCreatedTestData = wx.getStorageSync('hasCreatedTestData');
    if (hasCreatedTestData) return;
    
    // 创建聊天历史测试数据
    const chatHistory = [
      {
        id: 'chat1',
        query: '今天天气怎么样？',
        result: '今天天气晴朗，温度适宜，非常适合户外活动。',
        timestamp: Date.now() - 3600000,
        content: '今天天气怎么样？'
      },
      {
        id: 'chat2',
        query: '推荐一本好书',
        result: '我推荐《三体》，这是一部非常优秀的科幻小说，讲述了地球文明与三体文明的接触。',
        timestamp: Date.now() - 7200000,
        content: '推荐一本好书'
      }
    ];
    
    // 创建翻译历史测试数据
    const translateHistory = [
      {
        id: 'translate1',
        query: 'Hello world',
        result: '你好，世界',
        timestamp: Date.now() - 10800000,
        content: 'Hello world'
      }
    ];
    
    // 创建图像历史测试数据
    const imageHistory = [
      {
        id: 'image1',
        query: '识别图片内容',
        result: '图片中包含一只猫和一只狗',
        timestamp: Date.now() - 14400000,
        content: '识别图片内容',
        imagePath: '/images/translate-empty.png'
      }
    ];
    
    // 创建语音历史测试数据
    const voiceHistory = [
      {
        id: 'voice1',
        text: '这是通过语音识别转换的文字内容',
        time: '12:30',
        timestamp: Date.now() - 18000000
      }
    ];
    
    // 保存到本地存储
    wx.setStorageSync('chatHistory', chatHistory);
    wx.setStorageSync('translateHistory', translateHistory);
    wx.setStorageSync('imageHistory', imageHistory);
    wx.setStorageSync('voiceHistory', voiceHistory);
    wx.setStorageSync('hasCreatedTestData', true);
  },
  
  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    
    // 今天内的显示时间
    if (diff < 24 * 60 * 60 * 1000 && 
        date.getDate() === now.getDate()) {
      const hours = date.getHours();
      const minutes = date.getMinutes();
      return `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
    }
    
    // 昨天的显示"昨天"
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()) {
      return '昨天';
    }
    
    // 一周内的显示星期几
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      return weekdays[date.getDay()];
    }
    
    // 其他显示日期
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  },
  
  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    if (tab !== this.data.activeTab) {
      console.log('切换到标签:', tab);
      
      // 添加过渡动画
      this.setData({
        loading: true,
        activeTab: tab // 立即更新激活的标签
      });
      
      // 延迟加载数据，显示过渡效果
      setTimeout(() => {
        this.loadHistoryData(tab);
      }, 200);
    }
  },
  
  // 查看详细记录
  viewDetail(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.historyList[index];
    
    // 如果是游客模式，提示不能查看详情
    if (this.data.isGuestMode && item.type !== 'image') {
      wx.showToast({
        title: '游客模式下无法查看详情',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    switch (item.type) {
      case 'chat':
        wx.navigateTo({
          url: `/pages/chat/chat?history=${item.id}`
        });
        break;
      case 'translate':
        wx.navigateTo({
          url: `/pages/translate/translate?history=${item.id}`
        });
        break;
      case 'image':
        // 对于图像记录，直接显示图片和结果
        if (item.imagePath) {
          wx.previewImage({
            urls: [item.imagePath]
          });
        } else {
          wx.showToast({
            title: '图片不存在',
            icon: 'none'
          });
        }
        break;
      case 'voice':
        wx.navigateTo({
          url: `/pages/voice/voice?history=${item.id}`
        });
        break;
    }
  },
  
  // 删除历史记录项
  deleteItem(e) {
    // 如果是游客模式，提示不能删除
    if (this.data.isGuestMode) {
      wx.showModal({
        title: '提示',
        content: '游客模式下不能删除历史记录',
        showCancel: false
      });
      return;
    }
    
    const { index } = e.currentTarget.dataset;
    const item = this.data.historyList[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          // 根据类型从对应存储中删除
          const storageKey = `${item.type}History`;
          const historyItems = wx.getStorageSync(storageKey) || [];
          const newHistoryItems = historyItems.filter(h => h.id !== item.id);
          wx.setStorageSync(storageKey, newHistoryItems);
          
          // 更新视图（带动画效果）
          const newList = [...this.data.historyList];
          newList.splice(index, 1);
          
          this.setData({
            historyList: newList,
            isEmpty: newList.length === 0
          });
          
          // 更新标签页计数
          this.updateTabCounts();
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
    
    // 阻止事件冒泡
    return false;
  },
  
  // 更新标签页计数
  updateTabCounts() {
    const chatHistory = wx.getStorageSync('chatHistory') || [];
    const translateHistory = wx.getStorageSync('translateHistory') || [];
    const imageHistory = wx.getStorageSync('imageHistory') || [];
    const voiceHistory = wx.getStorageSync('voiceHistory') || [];
    
    const tabs = this.data.tabs.map(item => {
      if (item.id === 'all') {
        item.count = chatHistory.length + translateHistory.length + imageHistory.length + voiceHistory.length;
      } else if (item.id === 'chat') {
        item.count = chatHistory.length;
      } else if (item.id === 'translate') {
        item.count = translateHistory.length;
      } else if (item.id === 'image') {
        item.count = imageHistory.length;
      } else if (item.id === 'voice') {
        item.count = voiceHistory.length;
      }
      return item;
    });
    
    this.setData({ tabs });
  },
  
  // 清空所有历史记录
  clearAllHistory() {
    // 如果是游客模式，提示不能清空
    if (this.data.isGuestMode) {
      wx.showModal({
        title: '提示',
        content: '游客模式下不能清空历史记录',
        showCancel: false
      });
      return;
    }
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空' + (this.data.activeTab === 'all' ? '所有' : this.data.tabs.find(t => t.id === this.data.activeTab).name) + '历史记录吗？这将无法恢复！',
      success: (res) => {
        if (res.confirm) {
          // 清空所有历史记录
          const tab = this.data.activeTab;
          if (tab === 'all') {
            wx.removeStorageSync('chatHistory');
            wx.removeStorageSync('translateHistory');
            wx.removeStorageSync('imageHistory');
            wx.removeStorageSync('voiceHistory');
          } else {
            wx.removeStorageSync(`${tab}History`);
          }
          
          // 如果是游客模式，需要重新创建测试数据
          if (this.data.isGuestMode) {
            wx.removeStorageSync('hasCreatedTestData');
            this.createTestDataIfNeeded();
          }
          
          // 更新视图
          this.loadHistoryData();
          
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  }
})