{"description": "Project configuration file", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "3.7.3", "appid": "wx560bdd9f36610ae7", "projectname": "AI-Assistant", "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}}