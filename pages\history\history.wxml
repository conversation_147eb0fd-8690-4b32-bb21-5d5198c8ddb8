<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <view class="title-section">
      <text class="title">使用记录</text>
      <view class="subtitle">查看您的AI助手使用历史</view>
    </view>
    <!-- 游客模式提示 -->
    <view class="guest-mode-tip" wx:if="{{isGuestMode}}">
      <icon type="info" size="16" color="#ffffff"></icon>
      <view class="guest-tip-text">
        <text class="guest-label">游客模式</text>
        <text class="guest-tip-detail">不会保存记录</text>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs-container">
    <scroll-view scroll-x class="tabs" enable-flex show-scrollbar="{{false}}">
      <view 
        class="tab {{activeTab === item.id ? 'active' : ''}}" 
        wx:for="{{tabs}}" 
        wx:key="id" 
        data-tab="{{item.id}}" 
        bindtap="switchTab"
      >
        <text>{{item.name}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
        <view class="tab-line" wx:if="{{activeTab === item.id}}"></view>
      </view>
    </scroll-view>
  </view>

  <!-- 最后更新时间 -->
  <view class="refresh-time">
    <icon type="waiting" size="14" color="#999999"></icon>
    <text>{{lastRefreshTime}}</text>
  </view>

  <!-- 历史记录列表 -->
  <scroll-view scroll-y class="history-list" enable-flex refresher-enabled="{{true}}" refresher-triggered="{{loading}}" bindrefresherrefresh="onPullDownRefresh">
    <block wx:if="{{historyList.length > 0}}">
      <view class="history-item" wx:for="{{historyList}}" wx:key="id" hover-class="item-hover">
        <view class="item-content" bindtap="viewDetail" data-index="{{index}}">
          <view class="item-header">
            <view class="type-tag {{item.type}}">
              <text class="type-text">{{item.typeText}}</text>
            </view>
            <view class="time">{{item.time || '未知时间'}}</view>
          </view>
          
          <view class="content">
            <text class="query">{{item.query || item.content || '无内容'}}</text>
            <text class="result" wx:if="{{item.result}}">{{item.result.length > 50 ? item.result.substring(0, 50) + '...' : item.result}}</text>
          </view>

          <!-- 图片预览缩略图 -->
          <view class="image-preview" wx:if="{{item.type === 'image' && item.imagePath}}">
            <image src="{{item.imagePath}}" mode="aspectFill" class="preview-image"></image>
          </view>
        </view>
        
        <view class="actions" wx:if="{{!isGuestMode}}">
          <view class="action-btn delete" catchtap="deleteItem" data-index="{{index}}">
            <icon type="clear" size="18" color="#ff4d4f"></icon>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && isEmpty}}">
      <icon type="info" size="60" color="#cccccc"></icon>
      <text class="empty-title">暂无{{activeTab === 'all' ? '' : currentTabName}}记录</text>
      <view class="empty-tip">您还没有使用过AI助手的{{activeTab === 'all' ? '功能' : currentTabName + '功能'}}</view>
      <navigator url="/pages/index/index" open-type="switchTab" class="try-btn">立即体验</navigator>
    </view>
    
    <!-- 加载中状态 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </scroll-view>

  <!-- 底部操作区 -->
  <view class="footer-actions" wx:if="{{historyList.length > 0 && !isGuestMode}}">
    <button class="clear-btn" bindtap="clearAllHistory" hover-class="btn-hover">
      <icon type="clear" size="16" color="#ffffff"></icon>
      <text>清空{{activeTab === 'all' ? '所有' : currentTabName}}记录</text>
    </button>
  </view>
  
  <!-- 游客模式提示 -->
  <view class="footer-guest-tip" wx:if="{{isGuestMode}}">
    <text>游客模式下不能删除或清空记录</text>
    <navigator url="/pages/login/login" open-type="redirect" class="login-link">切换到微信登录</navigator>
  </view>
</view>