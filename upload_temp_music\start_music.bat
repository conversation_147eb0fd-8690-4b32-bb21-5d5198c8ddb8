@echo off
chcp 65001
title 微信小程序音乐服务
color 0A

echo ===================================
echo    微信小程序本地音乐服务启动工具
echo ===================================
echo.

REM 获取本机IP地址
ipconfig | findstr "IPv4"
echo.
echo 请确保config.js中设置了正确的IP地址：
echo 1. AI服务: apiBaseUrl: 'http://您的IP:11434'
echo 2. 音乐服务: musicApiBaseUrl: 'http://您的IP:5000'
echo.

REM 检查Python是否安装
python --version 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 未检测到Python，请安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b
)

REM 检查服务是否已在运行
netstat -ano | findstr ":5000" > nul
if %ERRORLEVEL% EQU 0 (
    echo [注意] 端口5000已被占用，可能音乐服务已在运行
    echo 如需重启服务，请先结束现有Python进程
    echo.
)

REM 检查必要的文件
if not exist "..\simple_local_music.py" (
    echo [错误] 未找到simple_local_music.py文件
    pause
    exit /b
)

REM 创建必要的目录
if not exist "..\download\music" mkdir "..\download\music"
if not exist "..\download\cover" mkdir "..\download\cover"

echo [信息] 正在启动音乐服务...
echo [信息] 服务启动后，请打开微信开发者工具测试音乐搜索功能
echo [信息] 可使用"晚风"、"流行"等关键词进行搜索测试
echo [信息] 首次使用请先添加本地音乐文件，可运行add_music.bat
echo.
echo 按Ctrl+C可停止服务
echo.

REM 启动音乐服务
cd ..
python simple_local_music.py 