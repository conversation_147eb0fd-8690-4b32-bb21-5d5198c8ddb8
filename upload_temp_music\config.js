/**
 * 全局配置文件
 * 在微信开发者工具中可以快速修改此文件来配置API地址
 */

// 判断是否在开发者工具中运行
// 微信小程序不支持process对象，改用直接配置方式
const inDevTool = true;  // 开发者工具调试时设为true，真机测试时设为false

module.exports = {
  // API服务地址 - 以下地址请根据实际环境修改
  // 1. 当使用电脑运行微信开发者工具时，可以使用localhost
  //apiBaseUrl: 'http://127.0.0.1:11434',  // Ollama服务默认端口是11434
  
  // 音乐服务API地址（端口5000）
  //musicApiBaseUrl: 'http://127.0.0.1:5000',  // 本地音乐服务端口是5000
  
  // 2. 当需要在手机上调试时，请使用电脑的实际IP地址
  // (使用命令ipconfig查看) 确保手机和电脑在同一网络
  // 可选的IP地址配置，取消注释使用
   apiBaseUrl: 'http://***********:11434',  // 这里填写您的实际IP地址
   musicApiBaseUrl: 'http://***********:5000',  // 这里填写您的实际IP地址
  
  // AI模型配置
  modelConfig: {
    model: 'gemma3:12b',       // 使用已安装的gemma3:12b模型
    temperature: 0.5,          // 降低温度以提高响应稳定性
    num_predict: 1000,         // 减少生成的最大长度，以加快响应速度
    system_prompt: "你是一个有用的AI助手。回答要简洁、专业。对于代码问题，给出简短但实用的示例。"
  },
  
  // 备选模型（当配置的模型不可用时）
  fallbackModels: [
    'gemma3:12b',      // Gemma 3模型
    'deepseek-r1:14b',
    'deepseek-r1:8b'
  ],
  
  // 图像识别模型配置
  imageModelConfig: {
    defaultModel: 'gemma3:12b',  // 默认使用Gemma 3模型进行图像识别
    temperature: 0.3,            // 降低温度以提高图像描述的准确性
    num_predict: 2000,           // 为图像描述增加输出长度
    // 不同模型的配置
    models: {
      'gemma3:12b': {
        label: 'Gemma 3 (高精度)',
        temperature: 0.3,
        num_predict: 2000,
        apiEndpoint: '/api/chat',
        supportsVision: true
      },
      'deepseek-r1:14b': {
        label: 'DeepSeek 14B (通用)',
        temperature: 0.5,
        num_predict: 1500,
        apiEndpoint: '/api/generate',
        supportsVision: false
      },
      'deepseek-r1:8b': {
        label: 'DeepSeek 8B (快速)',
        temperature: 0.7,
        num_predict: 1000,
        apiEndpoint: '/api/generate',
        supportsVision: false
      }
    }
  },
  
  // 请求配置
  requestConfig: {
    timeout: 60000,            // 请求超时时间（毫秒）
    retryCount: 2,             // 请求失败重试次数
    retryDelay: 1000,          // 重试间隔（毫秒）
  },
  
  // 语音配置 - 目前暂时禁用外部API
  voiceConfig: {
    enabled: false,           // 临时禁用语音API
    method: 'local',          // 使用本地模拟（'wx'=微信, 'baidu'=百度, 'local'=本地模拟）
    timeout: 10000            // 请求超时时间
  },
  
  // 工具函数：安全拼接URL路径，防止/api重复
  joinUrl: function(base, path) {
    // 确保基础URL没有尾随斜杠
    if (base.endsWith('/')) {
      base = base.slice(0, -1);
    }
    
    // 确保路径以斜杠开头
    if (!path.startsWith('/')) {
      path = '/' + path;
    }
    
    // 避免/api重复
    if (base.endsWith('/api') && path.startsWith('/api')) {
      return base + path.substring(4);
    }
    
    return base + path;
  }
}; 