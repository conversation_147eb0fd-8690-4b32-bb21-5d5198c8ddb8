.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.header {
  padding: 40rpx 30rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 游客模式提示样式 */
.guest-mode-tip {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  background: rgba(255, 152, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 152, 0, 0.3);
}

.guest-mode-tip text {
  font-size: 24rpx;
  color: #ff9800;
  font-weight: 500;
}

.guest-tip-detail {
  font-size: 20rpx !important;
  opacity: 0.8;
}

.tabs {
  display: flex;
  background-color: #fff;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.tab {
  position: relative;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.tab.active {
  color: #1677ff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 4rpx;
  background-color: #1677ff;
  border-radius: 2rpx;
}

.history-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.history-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  display: flex;
}

.item-content {
  flex: 1;
  padding: 24rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.type-tag {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  color: #fff;
}

.type-tag.chat {
  background-color: #1677ff;
}

.type-tag.translate {
  background-color: #13c2c2;
}

.type-tag.image {
  background-color: #722ed1;
}

.type-tag.voice {
  background-color: #eb2f96;
}

.type-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.content {
  display: flex;
  flex-direction: column;
}

.query {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.result {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.actions {
  display: flex;
  flex-direction: column;
  width: 80rpx;
  background-color: #f8f8f8;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.delete {
  background-color: #fff2f0;
}

.action-btn.delete:active {
  background-color: #ffccc7;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-state text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #bbb;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 28rpx;
  color: #999;
}

.footer-actions {
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 游客模式底部提示 */
.footer-guest-tip {
  padding: 20rpx 30rpx;
  background-color: #fff;
  text-align: center;
  font-size: 26rpx;
  color: #999;
  border-top: 1rpx solid #f0f0f0;
}

.login-link {
  color: #1677ff;
  text-decoration: underline;
  margin-left: 16rpx;
  display: inline-block;
  margin-top: 10rpx;
}