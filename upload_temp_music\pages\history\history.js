 // history.js
Page({
  data: {
    historyList: [],
    loading: true,
    activeTab: 'all', // 'all', 'chat', 'translate', 'image', 'voice'
    tabs: [
      { id: 'all', name: '全部' },
      { id: 'chat', name: '对话' },
      { id: 'translate', name: '翻译' },
      { id: 'image', name: '图像' },
      { id: 'voice', name: '语音' }
    ],
    isEmpty: false,
    isGuestMode: false // 新增：是否是游客模式
  },

  onLoad() {
    // 检查是否是游客模式
    const isGuestMode = wx.getStorageSync('isGuestMode') || false;
    this.setData({ isGuestMode });
    
    this.loadHistoryData();
  },

  onShow() {
    // 每次显示页面时重新检查游客模式
    const isGuestMode = wx.getStorageSync('isGuestMode') || false;
    if (isGuestMode !== this.data.isGuestMode) {
      this.setData({ isGuestMode });
    }
    
    // 每次显示页面时重新加载数据
    this.loadHistoryData();
  },

  onPullDownRefresh() {
    this.loadHistoryData();
    wx.stopPullDownRefresh();
  },

  loadHistoryData(tab = this.data.activeTab) {
    this.setData({ loading: true, isEmpty: false });
    
    // 创建测试数据（如果本地存储中没有数据）
    this.createTestDataIfNeeded();
    
    // 获取所有历史记录
    const chatHistory = wx.getStorageSync('chatHistory') || [];
    const translateHistory = wx.getStorageSync('translateHistory') || [];
    const imageHistory = wx.getStorageSync('imageHistory') || [];
    const voiceHistory = wx.getStorageSync('voiceHistory') || [];
    
    // 合并所有历史记录，并添加类型标识
    let allHistory = [];
    
    if (tab === 'all' || tab === 'chat') {
      allHistory = [...allHistory, ...chatHistory.map(item => ({ 
        ...item, 
        type: 'chat', 
        typeText: '对话', 
        icon: '/images/chat.png',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'translate') {
      allHistory = [...allHistory, ...translateHistory.map(item => ({ 
        ...item, 
        type: 'translate', 
        typeText: '翻译', 
        icon: '/images/translate.png',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'image') {
      allHistory = [...allHistory, ...imageHistory.map(item => ({ 
        ...item, 
        type: 'image', 
        typeText: '图像', 
        icon: '/images/image.png',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    if (tab === 'all' || tab === 'voice') {
      allHistory = [...allHistory, ...voiceHistory.map(item => ({ 
        ...item, 
        type: 'voice', 
        typeText: '语音', 
        icon: '/images/voice.png',
        time: this.formatTime(new Date(item.timestamp || Date.now()))
      }))];
    }
    
    // 按时间排序（从新到旧）
    allHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    
    this.setData({
      historyList: allHistory,
      activeTab: tab,
      loading: false,
      isEmpty: allHistory.length === 0
    });
  },
  
  // 创建测试数据（仅用于演示）
  createTestDataIfNeeded() {
    const hasCreatedTestData = wx.getStorageSync('hasCreatedTestData');
    if (hasCreatedTestData) return;
    
    // 创建聊天历史测试数据
    const chatHistory = [
      {
        id: 'chat1',
        query: '今天天气怎么样？',
        result: '今天天气晴朗，温度适宜，非常适合户外活动。',
        timestamp: Date.now() - 3600000,
        content: '今天天气怎么样？'
      },
      {
        id: 'chat2',
        query: '推荐一本好书',
        result: '我推荐《三体》，这是一部非常优秀的科幻小说，讲述了地球文明与三体文明的接触。',
        timestamp: Date.now() - 7200000,
        content: '推荐一本好书'
      }
    ];
    
    // 创建翻译历史测试数据
    const translateHistory = [
      {
        id: 'translate1',
        query: 'Hello world',
        result: '你好，世界',
        timestamp: Date.now() - 10800000,
        content: 'Hello world'
      }
    ];
    
    // 创建图像历史测试数据
    const imageHistory = [
      {
        id: 'image1',
        query: '识别图片内容',
        result: '图片中包含一只猫和一只狗',
        timestamp: Date.now() - 14400000,
        content: '识别图片内容',
        imagePath: '/images/default-cover.jpg'
      }
    ];
    
    // 创建语音历史测试数据
    const voiceHistory = [
      {
        id: 'voice1',
        text: '这是通过语音识别转换的文字内容',
        time: '12:30',
        timestamp: Date.now() - 18000000
      }
    ];
    
    // 保存到本地存储
    wx.setStorageSync('chatHistory', chatHistory);
    wx.setStorageSync('translateHistory', translateHistory);
    wx.setStorageSync('imageHistory', imageHistory);
    wx.setStorageSync('voiceHistory', voiceHistory);
    wx.setStorageSync('hasCreatedTestData', true);
  },
  
  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    
    // 今天内的显示时间
    if (diff < 24 * 60 * 60 * 1000 && 
        date.getDate() === now.getDate()) {
      const hours = date.getHours();
      const minutes = date.getMinutes();
      return `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
    }
    
    // 昨天的显示"昨天"
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()) {
      return '昨天';
    }
    
    // 一周内的显示星期几
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      return weekdays[date.getDay()];
    }
    
    // 其他显示日期
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  },
  
  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    if (tab !== this.data.activeTab) {
      this.loadHistoryData(tab);
    }
  },
  
  // 查看详细记录
  viewDetail(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.historyList[index];
    
    switch (item.type) {
      case 'chat':
        wx.navigateTo({
          url: `/pages/chat/chat?history=${item.id}`
        });
        break;
      case 'translate':
        wx.navigateTo({
          url: `/pages/translate/translate?history=${item.id}`
        });
        break;
      case 'image':
        // 对于图像记录，直接显示图片和结果
        if (item.imagePath) {
          wx.previewImage({
            urls: [item.imagePath]
          });
        } else {
          wx.showToast({
            title: '图片不存在',
            icon: 'none'
          });
        }
        break;
      case 'voice':
        wx.navigateTo({
          url: `/pages/voice/voice?history=${item.id}`
        });
        break;
    }
  },
  
  // 删除历史记录项
  deleteItem(e) {
    // 如果是游客模式，提示不能删除
    if (this.data.isGuestMode) {
      wx.showModal({
        title: '提示',
        content: '游客模式下不能删除历史记录',
        showCancel: false
      });
      return;
    }
    
    const { index } = e.currentTarget.dataset;
    const item = this.data.historyList[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          // 根据类型从对应存储中删除
          const storageKey = `${item.type}History`;
          const historyItems = wx.getStorageSync(storageKey) || [];
          const newHistoryItems = historyItems.filter(h => h.id !== item.id);
          wx.setStorageSync(storageKey, newHistoryItems);
          
          // 更新视图
          this.loadHistoryData();
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
    
    // 阻止事件冒泡
    return false;
  },
  
  // 清空所有历史记录
  clearAllHistory() {
    // 如果是游客模式，提示不能清空
    if (this.data.isGuestMode) {
      wx.showModal({
        title: '提示',
        content: '游客模式下不能清空历史记录',
        showCancel: false
      });
      return;
    }
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？这将无法恢复！',
      success: (res) => {
        if (res.confirm) {
          // 清空所有历史记录
          const tab = this.data.activeTab;
          if (tab === 'all') {
            wx.removeStorageSync('chatHistory');
            wx.removeStorageSync('translateHistory');
            wx.removeStorageSync('imageHistory');
            wx.removeStorageSync('voiceHistory');
          } else {
            wx.removeStorageSync(`${tab}History`);
          }
          
          // 如果是游客模式，需要重新创建测试数据
          if (this.data.isGuestMode) {
            wx.removeStorageSync('hasCreatedTestData');
            this.createTestDataIfNeeded();
          }
          
          // 更新视图
          this.loadHistoryData();
          
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  }
})