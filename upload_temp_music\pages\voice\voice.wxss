.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: env(safe-area-inset-bottom);
}

.page-header {
  padding: 40rpx 30rpx 20rpx;
  background: linear-gradient(135deg, #1677ff 0%, #3b95f2 100%);
  color: white;
  text-align: center;
}

.page-title {
  font-size: 38rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  opacity: 0.85;
}

.page-subtitle-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.recognition-method {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  transition: all 0.2s;
}

.recognition-method:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.method-text {
  margin-right: 8rpx;
}

.method-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 结果区域样式 */
.result-area {
  padding: 30rpx;
}

.result-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  min-height: 180rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.result-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150rpx;
  color: #8a9aa9;
  font-size: 28rpx;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.action-buttons {
  display: flex;
  margin-top: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  margin-left: 20rpx;
  background: rgba(22, 119, 255, 0.1);
  color: #1677ff;
  font-size: 26rpx;
}

.action-btn:active {
  background: rgba(22, 119, 255, 0.2);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 历史记录区域样式 */
.history-area {
  flex: 1;
  padding: 0 30rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e6e6e6;
}

.history-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.clear-history {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #8a9aa9;
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.history-content {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #eaeaea;
}

.history-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.history-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.history-time {
  font-size: 24rpx;
  color: #8a9aa9;
}

.history-actions {
  display: flex;
}

.history-action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.7;
}

.empty-history {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  color: #8a9aa9;
  font-size: 28rpx;
}

/* 录音按钮区域样式 */
.record-area {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(to bottom, rgba(245, 247, 250, 0), #f5f7fa);
}

.record-button {
  width: 180rpx;
  height: 180rpx;
  border-radius: 90rpx;
  background: linear-gradient(135deg, #1677ff 0%, #3b95f2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 26rpx;
  box-shadow: 0 6rpx 30rpx rgba(22, 119, 255, 0.3);
  transition: all 0.2s ease;
}

.record-button.recording {
  transform: scale(1.05);
  background: linear-gradient(135deg, #ff3a3a 0%, #ff6b6b 100%);
  box-shadow: 0 6rpx 30rpx rgba(255, 58, 58, 0.3);
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.record-tip {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #8a9aa9;
}

/* 加载遮罩样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 30rpx 60rpx;
  border-radius: 12rpx;
  color: #333;
  font-size: 30rpx;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
} 