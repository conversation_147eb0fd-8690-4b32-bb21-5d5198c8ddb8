<view class="container">
  <view class="header">
    <view class="title">使用记录</view>
    <!-- 游客模式提示 -->
    <view class="guest-mode-tip" wx:if="{{isGuestMode}}">
      <text>游客模式</text>
      <text class="guest-tip-detail">不会保存记录，仅显示示例数据</text>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === item.id ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="id" 
      data-tab="{{item.id}}" 
      bindtap="switchTab"
    >
      <text>{{item.name}}</text>
      <view class="tab-line" wx:if="{{activeTab === item.id}}"></view>
    </view>
  </view>

  <!-- 历史记录列表 -->
  <scroll-view scroll-y class="history-list" enable-flex>
    <block wx:if="{{historyList.length > 0}}">
      <view class="history-item" wx:for="{{historyList}}" wx:key="id">
        <view class="item-content" bindtap="viewDetail" data-index="{{index}}">
          <view class="item-header">
            <view class="type-tag {{item.type}}">
              <image class="type-icon" src="{{item.icon}}"></image>
              <text>{{item.typeText}}</text>
            </view>
            <view class="time">{{item.time || '未知时间'}}</view>
          </view>
          
          <view class="content">
            <text class="query">{{item.query || item.content || '无内容'}}</text>
            <text class="result" wx:if="{{item.result}}">{{item.result.length > 50 ? item.result.substring(0, 50) + '...' : item.result}}</text>
          </view>
        </view>
        
        <view class="actions" wx:if="{{!isGuestMode}}">
          <view class="action-btn delete" catchtap="deleteItem" data-index="{{index}}">
            <image src="/images/delete.png" class="action-icon"></image>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && isEmpty}}">
      <image src="/images/empty.png" class="empty-icon"></image>
      <text>暂无使用记录</text>
      <view class="empty-tip">您还没有使用过AI助手的功能</view>
    </view>
    
    <!-- 加载中状态 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </scroll-view>

  <!-- 底部操作区 -->
  <view class="footer-actions" wx:if="{{historyList.length > 0 && !isGuestMode}}">
    <button class="clear-btn" bindtap="clearAllHistory">
      <image src="/images/trash.png" class="btn-icon"></image>
      <text>清空{{activeTab === 'all' ? '所有' : ''}}记录</text>
    </button>
  </view>
  
  <!-- 游客模式提示 -->
  <view class="footer-guest-tip" wx:if="{{isGuestMode}}">
    <text>游客模式下不能删除或清空记录</text>
    <navigator url="/pages/login/login" open-type="redirect" class="login-link">切换到微信登录</navigator>
  </view>
</view>