/* 主容器 */
.music-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
  padding: 0 0 120rpx 0;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
}
  
/* 搜索框 */
.search-box {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 30rpx 30rpx 20rpx;
  background-color: rgba(248, 249, 252, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}
  
.search-input-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.search-input-container:focus-within {
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(26, 173, 25, 0.3);
}
  
.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}
  
.search-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1aad19;
}
  
.search-icon image {
  width: 40rpx;
  height: 40rpx;
}
  
/* 提示信息 */
.tip-message {
  padding: 20rpx 30rpx;
  margin: 20rpx 30rpx;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 12rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666;
  flex: 1;
  border-left: 6rpx solid rgba(64, 158, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.1);
}

.tip-message text {
  font-size: 26rpx;
  color: #409EFF;
  font-weight: 500;
}

/* 平台切换区域 */
.platform-section {
  padding: 20rpx 30rpx;
  margin-bottom: 10rpx;
}

.platform-switch {
  background: linear-gradient(135deg, #1aad19, #39b54a);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  margin: 20rpx 0;
  box-shadow: 0 6rpx 16rpx rgba(26, 173, 25, 0.2);
  transition: all 0.3s;
}

.platform-switch:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(26, 173, 25, 0.15);
}

/* 搜索历史 */
.history-section {
  padding: 20rpx 30rpx;
  margin-bottom: 10rpx;
}
  
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
  
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 32rpx;
  width: 6rpx;
  background: linear-gradient(to bottom, #1aad19, #4cd964);
  border-radius: 6rpx;
}
  
.clear-history {
  font-size: 24rpx;
  color: #999;
  padding: 6rpx 14rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  transition: all 0.3s;
}

.clear-history:active {
  background-color: #e0e0e0;
}
  
.history-list {
  display: flex;
  flex-wrap: wrap;
}
  
.history-item {
  background-color: #fff;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  margin: 10rpx 20rpx 10rpx 0;
  font-size: 26rpx;
  color: #555;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.history-item:active {
  background-color: #f5f5f5;
  transform: scale(0.97);
}
  
/* 搜索结果 */
.results-section {
  flex: 1;
  padding: 20rpx 30rpx;
}

.result-count {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}
  
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}
  
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(26, 173, 25, 0.2);
  border-left-color: #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
  
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
  
.song-list {
  margin-top: 20rpx;
}

/* 歌曲列表项 */
.song-item {
  display: flex;
  padding: 20rpx;
  margin: 16rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.song-item:active {
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  transform: translateY(2rpx);
  background-color: #fafafa;
}

.song-item-active {
  background-color: #f0f9f0;
  border-left: 6rpx solid #1aad19;
}

.song-item::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4rpx;
  background: linear-gradient(to bottom, #1aad19, #4cd964);
  opacity: 0.8;
}

.song-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
  
.song-info {
  flex: 1;
  overflow: hidden;
  padding-left: 16rpx;
}
  
.song-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
  
.song-artist {
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
  
.song-play-icon {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1aad19;
}

.song-play-icon image {
  width: 50rpx;
  height: 50rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 高级播放器样式 */
.player-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -2rpx 20rpx rgba(0,0,0,0.1);
  border-radius: 30rpx 30rpx 0 0;
  z-index: 100;
  overflow: hidden;
  padding: 30rpx;
  box-sizing: border-box;
}

.player-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(30rpx);
  opacity: 0.3;
  z-index: -1;
  transform: scale(1.2);
}

.player-content {
  position: relative;
  z-index: 2;
}

.player-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.player-cover {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
  margin-right: 30rpx;
  transition: transform 0.3s ease;
}

.rotate {
  animation: rotation 8s linear infinite;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.player-info {
  flex: 1;
  overflow: hidden;
}

.player-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.player-singer {
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 波形动画 */
.wave-animation {
  display: flex;
  align-items: flex-end;
  height: 30rpx;
  margin-left: 20rpx;
}

.wave-bar {
  width: 6rpx;
  background-color: #1aad19;
  margin: 0 2rpx;
  border-radius: 3rpx;
  animation: wave 0.5s infinite ease-in-out alternate;
}

.wave-bar:nth-child(1) {
  height: 20rpx;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 30rpx;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 16rpx;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 26rpx;
  animation-delay: 0.6s;
}

@keyframes wave {
  from {
    transform: scaleY(0.6);
  }
  to {
    transform: scaleY(1);
  }
}

/* 进度条容器 */
.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  min-width: 70rpx;
}

.slider-container {
  flex: 1;
  margin: 0 20rpx;
}

/* 控制面板 */
.control-panel {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20rpx 0;
}

.control-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255,255,255,0.8);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.2s;
}

.control-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.play-button {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #1aad19, #39b54a);
}

.control-button image {
  width: 40rpx;
  height: 40rpx;
}

.play-button image {
  width: 50rpx;
  height: 50rpx;
  filter: brightness(10);
}

/* 迷你播放器 */
.mini-player {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  z-index: 99;
}

.mini-player-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20rpx;
}

.mini-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.mini-info {
  flex: 1;
  overflow: hidden;
}

.mini-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mini-singer {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mini-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #f0f0f0;
}

.mini-progress-bar {
  height: 100%;
  background: linear-gradient(to right, #1aad19, #39b54a);
  transition: width 0.1s linear;
}

.mini-controls {
  margin-left: 20rpx;
}

.mini-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.mini-button image {
  width: 32rpx;
  height: 32rpx;
}

